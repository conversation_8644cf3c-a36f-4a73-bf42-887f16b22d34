<?php

namespace app\iterate\controller;

use app\iterate\model\IterationModel;
use app\work_items\logic\WorkItemsEsLogic;
use basic\BaseController;
use think\App;
use utils\Ctx;

class <PERSON>a extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);

    }
    public function fillEstimateTime()
    {
        $startDate = $this->request->param('start_date');
        $endDate = $this->request->param('end_date');


        $query = IterationModel::where('is_delete', 0)
            ->where(function ($query) {
                $query->whereNull('estimate_start_time')
                    ->whereOr('estimate_start_time', '=', '1971-01-01 00:00:00')
                    ->whereOr(function ($query) {
                        $query->whereNull('estimate_end_time')
                            ->whereOr('estimate_end_time', '=', '1971-01-01 00:00:00');
                    });
            });
//        dd($query->select()->where('iteration_id', 4014));
        if ($startDate && $endDate) {
            $query->whereBetween('create_at', [$startDate, $endDate]);
        }

        $iterations = $query->select();

        $esLogic = WorkItemsEsLogic::getInstance();
        $esClient = invade($esLogic)->esClient;
        $esIndex = invade($esLogic)->esIndex;
//        dd($iterations->column('iteration_id'));
//        $iterations = [$iterations->last()];
        foreach ($iterations as $iteration) {
            $params = [
                'index' => $esIndex,
                'body'  => [
                    'query' => [
                        'bool' => [
                            'filter' => [
                                ['term' => ['iteration_id' => $iteration->iteration_id]],
                                ['term' => ['is_delete' => 0]],
                            ]
                        ]
                    ],
                    'aggs'  => [
                        'min_time' => [
                            'min' => [
                                'script' => [
                                    'source' => "Math.min(doc['create_at'].value.toInstant().toEpochMilli(), doc.containsKey('update_at') && doc['update_at'].size() > 0 ? doc['update_at'].value.toInstant().toEpochMilli() : doc['create_at'].value.toInstant().toEpochMilli())"
                                ]
                            ]
                        ],
                        'max_time' => [
                            'max' => [
                                'script' => [
                                    'source' => "Math.max(doc['create_at'].value.toInstant().toEpochMilli(), doc.containsKey('update_at') && doc['update_at'].size() > 0 ? doc['update_at'].value.toInstant().toEpochMilli() : doc['create_at'].value.toInstant().toEpochMilli())"
                                ]
                            ]
                        ]
                    ],
                    'size'  => 0
                ]
            ];

            $response = $esClient->search($params)->asArray();
//            dd($response);

            $aggregations = $response['aggregations'] ?? null;
            if ( ! $aggregations) {
                continue;
            }

            $minTimeValue = $aggregations['min_time']['value'] ?? null;
            $maxTimeValue = $aggregations['max_time']['value'] ?? null;

            if ($minTimeValue && $maxTimeValue) {
                // 将时间戳转换为日期时间字符串
                $minTime = date('Y-m-d H:i:s', $minTimeValue / 1000); // ES返回的是毫秒，需要除以1000
                $maxTime = date('Y-m-d H:i:s', $maxTimeValue / 1000);

                $extends = json_decode($iteration->extends, true) ?: [];
                $extends['estimate_iteration_cycle'] = [
                    date('Y-m-d', $minTimeValue / 1000),
                    date('Y-m-d', $maxTimeValue / 1000),
                ];
                $iteration->estimate_start_time = $minTime;
                $iteration->estimate_end_time = $maxTime;
                $iteration->extends = json_encode($extends, JSON_UNESCAPED_UNICODE);
                $iteration->save();
            }

        }

        return json(['code' => 0, 'msg' => 'success']);
    }
}