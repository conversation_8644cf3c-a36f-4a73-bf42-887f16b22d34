<?php
/**
 * 测试contents字段合并功能
 * 
 * 这个测试文件用于验证WorkItemsEsLogic::mergeContents方法的修复是否正确工作
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\work_items\logic\WorkItemsEsLogic;

// 模拟测试数据
function testMergeContents() {
    echo "开始测试contents字段合并功能...\n";
    
    // 测试用例1：正常情况 - 所有cnt_id都存在于MySQL中
    echo "\n测试用例1：正常情况\n";
    $testData1 = [
        ['cnt_id' => 1, 'title' => '需求1'],
        ['cnt_id' => 2, 'title' => '任务1'],
        ['cnt_id' => 3, 'title' => '缺陷1']
    ];
    
    // 测试用例2：部分cnt_id不存在于MySQL中
    echo "\n测试用例2：部分cnt_id不存在\n";
    $testData2 = [
        ['cnt_id' => 1, 'title' => '需求1'],
        ['cnt_id' => 999, 'title' => '不存在的工作项'], // 这个cnt_id在MySQL中不存在
        ['cnt_id' => 3, 'title' => '缺陷1']
    ];
    
    // 测试用例3：空的cnt_id
    echo "\n测试用例3：包含空cnt_id\n";
    $testData3 = [
        ['cnt_id' => 1, 'title' => '需求1'],
        ['cnt_id' => null, 'title' => '空ID工作项'],
        ['cnt_id' => '', 'title' => '空字符串ID工作项'],
        ['cnt_id' => 3, 'title' => '缺陷1']
    ];
    
    // 测试用例4：空数组
    echo "\n测试用例4：空数组\n";
    $testData4 = [];
    
    echo "\n所有测试用例准备完成。\n";
    echo "注意：实际测试需要连接到数据库和Elasticsearch，这里只是展示测试结构。\n";
    echo "在实际环境中运行时，修复后的mergeContents方法应该能够：\n";
    echo "1. 正确处理存在的cnt_id，从MySQL获取contents字段\n";
    echo "2. 对于不存在的cnt_id，设置contents为空字符串而不是报错\n";
    echo "3. 过滤掉空的cnt_id，避免无效查询\n";
    echo "4. 正确处理空数组输入\n";
    
    return true;
}

// 运行测试
testMergeContents();

echo "\n测试完成！\n";
echo "修复内容总结：\n";
echo "1. 在mergeContents方法中添加了cnt_id的空值过滤\n";
echo "2. 使用安全的数组访问方式，避免访问不存在的键时报错\n";
echo "3. 为不存在的contents字段设置默认空字符串值\n";
echo "4. 改进了错误处理，使方法更加健壮\n";
?>
